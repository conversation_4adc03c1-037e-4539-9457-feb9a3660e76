{"name": "@sw-ecom360/common-module", "version": "1.0.33", "description": "", "author": "Social Unlimited Group", "license": "UNLICENSED", "type": "commonjs", "main": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist", "bin", "README.md", "CHANGELOG.md"], "exports": {".": {"types": "./dist/index.d.ts", "require": "./dist/index.js", "import": "./dist/index.js"}}, "repository": {"type": "git", "url": "https://github.com/sw-ecom360/bepkg-common.git"}, "publishConfig": {"registry": "https://npm.pkg.github.com/", "access": "restricted"}, "engines": {"node": ">=22", "pnpm": ">=10"}, "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\" \"libs/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "peerDependencies": {"@nestjs/common": "^11.0.1", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.1", "@nestjs/graphql": "^13.1.0", "@nestjs/mapped-types": "^2.1.0", "@nestjs/platform-express": "^11.0.1", "@prisma/client": "^6.14.0", "aws-jwt-verify": "^5.1.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "lodash": "^4.17.21", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "uuid": "^10.0.0", "@aws-sdk/client-s3": "^3.884.0", "@aws-sdk/s3-request-presigner": "3.884.0"}, "devDependencies": {"@eslint/eslintrc": "latest", "@eslint/js": "^9.18.0", "@graphql-codegen/cli": "^5.0.7", "@graphql-codegen/typescript": "^4.1.6", "@nestjs/cli": "^11.0.0", "@nestjs/common": "^11.1.6", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.6", "@nestjs/graphql": "^13.1.0", "@nestjs/mapped-types": "^2.1.0", "@nestjs/platform-express": "^11.1.6", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@prisma/client": "^6.16.2", "@sw-ecom360/lint-config": "^1.1.13", "@swc/cli": "^0.7.8", "@types/express": "^5.0.0", "@types/jest": "^30.0.0", "@types/lodash": "^4.17.20", "@types/node": "^22.10.7", "@types/supertest": "^6.0.2", "aws-jwt-verify": "^5.1.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^16.0.0", "jest": "^30.0.0", "lodash": "^4.17.21", "prettier": "^3.4.2", "prisma": "^6.14.0", "reflect-metadata": "^0.2.2", "rimraf": "^6.0.1", "rxjs": "^7.8.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "./coverage", "testEnvironment": "node", "roots": ["<rootDir>/src/"]}, "pnpm": {"onlyBuiltDependencies": ["@nestjs/core", "@prisma/client", "@prisma/engines", "@swc/core", "prisma", "unrs-resolver"]}}