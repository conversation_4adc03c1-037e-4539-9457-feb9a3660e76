import { Modu<PERSON> } from "@nestjs/common";
import { CommonModule } from "@sw-ecom360/common-module";
import { PrismaModule } from "src/prisma/prisma.module";
import { PrismaService } from "src/prisma/prisma.service";
import { CollectionsResolver } from "./collections.resolver";
import { CollectionRepository } from "./repositories/collection.repository";
import { CollectionsService } from "./services/collections.service";

@Module({
  imports: [CommonModule.forRoot(PrismaService), PrismaModule],
  providers: [CollectionsResolver, CollectionsService, CollectionRepository],
})
export class CollectionsModule {}
