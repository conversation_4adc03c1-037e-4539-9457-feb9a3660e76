import { <PERSON>rgs, <PERSON>sol<PERSON>, Query, Mutation } from "@nestjs/graphql";
import { PaginationInput } from "@sw-ecom360/common-module";
import { CollectionUniqueInput } from "./dto/collection-unique.input";
import { CollectionWhereInput } from "./dto/collection-where.input";
import { CreateCollectionInput } from "./dto/create-collection.input";
import { CollectionsService } from "./services/collections.service";

@Resolver("Collection")
export class CollectionsResolver {
  constructor(private readonly collectionsService: CollectionsService) {}

  @Query("collections")
  async findAll(
    @Args("pagination") pagination: PaginationInput,
    @Args("where") where: CollectionWhereInput,
  ) {
    return await this.collectionsService.findAllPaginated(pagination, where);
  }

  @Query("collection")
  async findOne(@Args("where") where: CollectionUniqueInput) {
    return await this.collectionsService.findOne(where);
  }

  @Mutation("removeCollection")
  remove(@Args("id") id: number) {
    return this.collectionsService.remove(id);
  }

  @Mutation("createCollection")
  create(@Args("createCollectionInput") input: CreateCollectionInput) {
    return this.collectionsService.create(input);
  }
}
