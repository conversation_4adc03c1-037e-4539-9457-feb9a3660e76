export function isObject(value: unknown): value is Record<string, unknown> {
  return typeof value === "object" && value !== null;
}

export function assertIsObject(
  value: unknown,
  message = "Value must be an object",
): asserts value is Record<string, unknown> {
  if (!isObject(value)) {
    throw new Error(message);
  }
}

export function assertIsString(
  value: unknown,
  message = "Value must be a string",
): asserts value is string {
  if (typeof value !== "string") {
    throw new Error(message);
  }
}

export function isStringOrNumber(value: unknown): value is string | number {
  return typeof value === "string" || typeof value === "number";
}

export function isNullOrUndefined(value: unknown): value is null | undefined {
  return value === null || value === undefined;
}

export function isTypedArray<T>(
  value: unknown,
  elementGuard: (element: unknown) => element is T,
): value is T[] {
  return Array.isArray(value) && value.every(elementGuard);
}
