import { Expose, Type } from "class-transformer";
import { VariantOptions } from "types/prisma-types";
import BaseEntity from "./base.entity";
import { ProductMedia } from "./product-media.entity";
import { Style } from "./style.entity";

export class Product extends BaseEntity {
  @Expose()
  sku: string;

  @Expose()
  productName: string;

  @Expose()
  @Type(() => ProductMedia)
  media: ProductMedia[];

  @Expose()
  @Type(() => Object)
  options?: VariantOptions;

  @Expose()
  styleId: bigint;

  @Expose()
  @Type(() => Style)
  style: Style;
}
