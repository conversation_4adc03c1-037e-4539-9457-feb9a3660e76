import { Expose, Type } from "class-transformer";
import BaseEntity from "./base.entity";
import { ProductMedia } from "./product-media.entity";
import { Product } from "./product.entity";

export class Style extends BaseEntity {
  @Expose()
  styleName: string;

  @Expose()
  @Type(() => ProductMedia)
  media: ProductMedia[];

  @Expose()
  publishedToShopify: boolean;

  @Expose()
  @Type(() => Product)
  products?: Product[];
}
