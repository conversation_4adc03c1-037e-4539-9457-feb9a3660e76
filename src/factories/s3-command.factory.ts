import {
  PutObjectCommand,
  GetO<PERSON><PERSON>ommand,
  DeleteObjectCommand,
  PutObjectCommandInput,
  GetObjectCommandInput,
  DeleteObjectCommandInput,
} from "@aws-sdk/client-s3";

// S3 Command Factories Interface
export interface IS3CommandFactories {
  putObject: (input: PutObjectCommandInput) => PutObjectCommand;
  getObject: (input: GetObjectCommandInput) => GetObjectCommand;
  deleteObject: (input: DeleteObjectCommandInput) => DeleteObjectCommand;
}

// Injection token
export const S3_COMMAND_FACTORIES = "S3_COMMAND_FACTORIES";

// Factory implementation
export const s3CommandFactories: IS3CommandFactories = {
  putObject: (input: PutObjectCommandInput) => new PutObjectCommand(input),
  getObject: (input: GetObjectCommandInput) => new GetObjectCommand(input),
  deleteObject: (input: DeleteObjectCommandInput) =>
    new DeleteObjectCommand(input),
};

// Provider factory for dependency injection
export const S3CommandFactoriesProvider = {
  provide: S3_COMMAND_FACTORIES,
  useValue: s3CommandFactories,
};
