import { Modu<PERSON> } from "@nestjs/common";
import { ConfigModule, ConfigType } from "@nestjs/config";
import { SqsModule } from "@ssut/nestjs-sqs";
import awsConfig from "../config/configuration/aws.config";

@Module({
  imports: [
    ConfigModule.forFeature(awsConfig),
    SqsModule.registerAsync({
      inject: [awsConfig.KEY],
      useFactory: (cfg: ConfigType<typeof awsConfig>) => {
        if (
          cfg.productsQueueUrl === undefined ||
          cfg.productsQueueUrl === "" ||
          cfg.fileUploadQueueUrl === undefined ||
          cfg.fileUploadQueueUrl === "" ||
          cfg.shopifyProductsQueueUrl === undefined ||
          cfg.shopifyProductsQueueUrl === ""
        ) {
          throw new Error(
            "AWS_SQS_PRODUCTS_QUEUE_URL, AWS_SQS_PRODUCT_FILE_UPLOAD_QUEUE_URL, and AWS_SQS_SHOPIFY_PRODUCTS_QUEUE_URL must be set",
          );
        }
        return {
          consumers: [
            {
              name: "excel-products",
              queueUrl: cfg.productsQueueUrl,
              region: cfg.region,
              waitTimeSeconds: 20,
            },
            {
              name: "product-file-upload",
              queueUrl: cfg.fileUploadQueueUrl,
              region: cfg.region,
              waitTimeSeconds: 20,
            },
            {
              name: "shopify-products",
              queueUrl: cfg.shopifyProductsQueueUrl,
              region: cfg.region,
              waitTimeSeconds: 20,
            },
          ],
          producers: [
            {
              name: "excel-products",
              queueUrl: cfg.productsQueueUrl,
              region: cfg.region,
            },
            {
              name: "product-file-upload",
              queueUrl: cfg.fileUploadQueueUrl,
              region: cfg.region,
            },
            {
              name: "shopify-products",
              queueUrl: cfg.shopifyProductsQueueUrl,
              region: cfg.region,
            },
          ],
        };
      },
    }),
  ],
  exports: [SqsModule],
})
export class AppSqsModule {}
