import { join } from "path";
import {
  YogaF<PERSON>rationDriver,
  YogaFederationDriverConfig,
} from "@graphql-yoga/nestjs-federation";
import { Module } from "@nestjs/common";
import { ConfigModule, ConfigService, ConfigType } from "@nestjs/config";
import { APP_GUARD } from "@nestjs/core";
import { GraphQLModule } from "@nestjs/graphql";
import {
  CognitoAuthGuard,
  CognitoAuthModule,
  CommonModule,
} from "@sw-ecom360/common-module";
import {
  ShopifyAdminConfigOptionsService,
  ShopifyAdminModule,
} from "@sw-ecom360/shopify-admin-api";
import {
  BigIntResolver,
  DateTimeResolver,
  JSONResolver,
} from "graphql-scalars";
import { CollectionsModule } from "./collections/collections.module";
import appConfig from "./config/configuration/app.config";
import awsConfig from "./config/configuration/aws.config";
import databaseConfig from "./config/configuration/database.config";
import graphqlConfig from "./config/configuration/graphql.config";
import microserviceConfig from "./config/configuration/microservice.config";
import { validate as validateConfig } from "./config/validation";
import { HealthController } from "./health/health.controller";
import { InterserviceModule } from "./interservice/interservice.module";
import { PrismaService } from "./prisma/prisma.service";
import { ProductModule } from "./product/product.module";
import { ProductFileModule } from "./product-file/product-file.module";
import { ShopModule } from "./shop/shop.module";
import { ShopifyProductsModule } from "./shopify-products/shopify-products.module";
import { ShopifyVariantsModule } from "./shopify-variants/shopify-variants.module";
import { StyleModule } from "./style/style.module";

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      cache: true,
      expandVariables: true,
      envFilePath:
        process.env.NODE_ENV === "production"
          ? undefined
          : [
              `.env.${process.env.NODE_ENV}.local`,
              `.env.${process.env.NODE_ENV}`,
              ".env.local",
              ".env",
            ],
      load: [
        appConfig,
        awsConfig,
        databaseConfig,
        graphqlConfig,
        microserviceConfig,
      ],
      validate: validateConfig,
      ignoreEnvFile: process.env.NODE_ENV === "production",
    }),
    GraphQLModule.forRootAsync<YogaFederationDriverConfig>({
      driver: YogaFederationDriver,
      inject: [appConfig.KEY, graphqlConfig.KEY],
      useFactory: (
        appConf: ConfigType<typeof appConfig>,
        graphqlConf: ConfigType<typeof graphqlConfig>,
      ) => ({
        path: `${appConf.globalPrefix}${graphqlConf.graphqlPath}`,
        typePaths: ["**/*.graphql"],
        definitions: {
          path: join(process.cwd(), "generated/api/graphql.ts"),
          outputAs: "class",
          defaultScalarType: "unknown",
          skipResolverArgs: false,
          customScalarTypeMapping: {
            BigInt: "bigint",
            DateTime: "Date",
            JSON: "Record<string, unknown>",
          },
        },
        resolverValidationOptions: {
          requireResolversForArgs: "error",
          requireResolversForNonScalar: "error",
          requireResolversForAllFields: "error",
          requireResolversForResolveType: "ignore",
          requireResolversToMatchSchema: "error",
        },
        inheritResolversFromInterfaces: true,
        resolvers: {
          BigInt: BigIntResolver,
          DateTime: DateTimeResolver,
          JSON: JSONResolver,
        },
        debug: process.env.NODE_ENV === "development",
        graphiql: process.env.NODE_ENV !== "production",
      }),
    }),
    ShopifyAdminModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useClass: ShopifyAdminConfigOptionsService,
    }),
    CommonModule.forRoot(PrismaService),
    ProductModule,
    CollectionsModule,
    StyleModule,
    ShopifyProductsModule,
    ShopifyVariantsModule,
    ProductFileModule,
    ShopModule,
    InterserviceModule,
    CognitoAuthModule,
  ],
  controllers: [HealthController],
  providers: [
    {
      provide: APP_GUARD,
      useClass: CognitoAuthGuard,
    },
  ],
})
export class AppModule {}
