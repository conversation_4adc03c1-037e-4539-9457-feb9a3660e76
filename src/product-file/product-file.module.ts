import { Module } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";
import { CommonModule } from "@sw-ecom360/common-module";
import { PrismaModule } from "src/prisma/prisma.module";
import { PrismaService } from "src/prisma/prisma.service";
import { ProductModule } from "src/product/product.module";
import { StyleModule } from "src/style/style.module";
import awsConfig from "../config/configuration/aws.config";
import { AppSqsModule } from "../queue/sqs.module";
import { ShopifyModule } from "../shopify/shopify.module";
import { ExcelParserPipe } from "./pipes/excel-parser.pipe";
import { ProductFileResolver } from "./product-file.resolver";
import { ProductFileRepository } from "./repositories/product-file.repository";
import { ProductFileConsumerService } from "./services/product-file-consumer.service";
import { ProductFileService } from "./services/product-file.service";

@Module({
  imports: [
    PrismaModule,
    CommonModule.forRoot(PrismaService),
    AppSqsModule,
    ConfigModule.forFeature(awsConfig),
    ShopifyModule,
    StyleModule,
    ProductModule,
  ],
  providers: [
    ProductFileResolver,
    ProductFileService,
    ProductFileRepository,
    ProductFileConsumerService,
    ExcelParserPipe,
  ],
  exports: [ProductFileService],
})
export class ProductFileModule {}
