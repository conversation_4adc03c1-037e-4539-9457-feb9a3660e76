import { Message } from "@aws-sdk/client-sqs";
import { Inject, Injectable, Logger } from "@nestjs/common";
import { ConfigType } from "@nestjs/config";
import { SqsMessageHandler, SqsService } from "@ssut/nestjs-sqs";
import { S3Service } from "@sw-ecom360/common-module";
import { isNil } from "lodash";
import { v4 as uuidv4 } from "uuid";
import awsConfig from "../../config/configuration/aws.config";
import { ExcelParserPipe } from "../pipes/excel-parser.pipe";
import { ProductFileRepository } from "../repositories/product-file.repository";
import { IProductFileMessage } from "../types/message.types";
import { ProductFileStatus } from "../types/product-file-status.types";

@Injectable()
export class ProductFileConsumerService {
  private readonly logger = new Logger(ProductFileConsumerService.name);

  constructor(
    private readonly fileRepository: ProductFileRepository,
    private readonly s3Service: S3Service,
    private readonly excelParserPipe: ExcelParserPipe,
    private readonly sqsService: SqsService,
    @Inject(awsConfig.KEY)
    private readonly config: ConfigType<typeof awsConfig>,
  ) {}

  @SqsMessageHandler("product-file-upload", false)
  async processFileMessage(message: Message): Promise<void> {
    let messageData: IProductFileMessage | null = null;

    try {
      if (isNil(message.Body) || message.Body.trim() === "") {
        this.logger.error("Received message without body");
        return;
      }

      messageData = JSON.parse(message.Body) as IProductFileMessage;
      this.logger.log(
        `${messageData.action} - Processing file message for file ID: ${messageData.fileId}`,
      );

      if (isNil(messageData)) {
        this.logger.error("Received message without file ID");
        return;
      }

      await this.fileRepository.updateStatus(
        messageData.fileId,
        ProductFileStatus.PROCESSING,
      );

      await this.processExcelFile(messageData);

      await this.fileRepository.updateStatus(
        messageData.fileId,
        ProductFileStatus.COMPLETED,
      );

      this.logger.log(
        `${messageData.action} - File processing completed for file ID: ${messageData.fileId}`,
      );
    } catch (error) {
      this.logger.error(`File processing failed: ${error}`);

      // Only try to update status if we have valid messageData
      if (messageData && messageData.fileId) {
        try {
          await this.fileRepository.updateStatus(
            messageData.fileId,
            ProductFileStatus.FAILED,
          );
        } catch (updateError) {
          this.logger.error(
            `Failed to update file status to FAILED: ${updateError}`,
          );
        }
      }

      throw error; // Re-throw to let SQS handle retry logic
    }
  }

  private async processExcelFile(
    messageData: IProductFileMessage,
  ): Promise<void> {
    try {
      this.logger.log(`Downloading file from S3: ${messageData.s3Key}`);

      const bucketName = this.config.fileUploadBucket;
      if (isNil(bucketName) || bucketName.trim() === "") {
        throw new Error("File upload bucket not configured");
      }

      const buffer = await this.s3Service.downloadFile(
        messageData.s3Key,
        bucketName,
      );

      const parsedStyles = this.excelParserPipe.transform(buffer);
      this.logger.log(`Parsed ${parsedStyles.length} styles from Excel`);

      const shopKey =
        process.env.SHOP_KEY ?? process.env.SHOPIFY_SHOP_KEY ?? "dev1";

      // Send each style as a separate message to the styles queue
      for (const style of parsedStyles) {
        await this.sendStyleMessage({
          styleName: style.styleName,
          color: style.color,
          products: style.products,
          sizes: style.sizes,
          shopKey,
          action: "PROCESS_EXCEL_STYLE",
        });
      }

      this.logger.log(
        `Sent ${parsedStyles.length} product messages to products queue`,
      );
    } catch (error) {
      this.logger.error(`Failed to process Excel file: ${error}`);
      throw error;
    }
  }

  private async sendStyleMessage(message: {
    styleName: string;
    color: string;
    products: { productName: string; sku: string; size: string }[];
    sizes: string[];
    shopKey: string;
    action: "PROCESS_EXCEL_STYLE";
  }): Promise<void> {
    const queueUrl = this.config.productsQueueUrl;

    if (queueUrl === undefined) {
      this.logger.error("Products queue URL not configured");
      throw new Error("Products queue URL not configured");
    }

    try {
      await this.sqsService.send("excel-products", {
        id: uuidv4(),
        body: JSON.stringify(message),
        messageAttributes: {
          action: { DataType: "String", StringValue: message.action },
          styleName: {
            DataType: "String",
            StringValue: message.styleName,
          },
          shopKey: {
            DataType: "String",
            StringValue: message.shopKey,
          },
        },
      });

      this.logger.log(`Style message sent for: ${message.styleName}`);
    } catch (error) {
      this.logger.error(`Failed to send style message: ${error}`);
      throw error;
    }
  }
}
