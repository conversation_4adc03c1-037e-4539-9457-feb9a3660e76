import { Inject, Injectable, Logger } from "@nestjs/common";
import { ConfigType } from "@nestjs/config";
import { SqsService } from "@ssut/nestjs-sqs";
import { S3Service } from "@sw-ecom360/common-module";
import { isNil } from "lodash";
import awsConfig from "../../config/configuration/aws.config";
import { CreateProductFileDto } from "../dto/create-product-file.dto";
import { ProductFileResponseDto } from "../dto/product-file-response.dto";
import { ProductFileRepository } from "../repositories/product-file.repository";
import { IProductFileMessage } from "../types/message.types";
import { ProductFileStatus } from "../types/product-file-status.types";

@Injectable()
export class ProductFileService {
  private readonly logger = new Logger(ProductFileService.name);

  constructor(
    private readonly fileRepository: ProductFileRepository,
    private readonly s3Service: S3Service,
    private readonly sqsService: SqsService,
    @Inject(awsConfig.KEY)
    private readonly config: ConfigType<typeof awsConfig>,
  ) {}

  async uploadExcelFile(
    file: Buffer,
    fileName: string,
  ): Promise<ProductFileResponseDto> {
    try {
      this.logger.log(`Uploading file: ${fileName}`);
      console.log(this.config);
      const bucketName = this.config.fileUploadBucket;
      if (isNil(bucketName) || bucketName.trim() === "") {
        throw new Error("File upload bucket not configured");
      }

      const { s3Key, s3Url } = await this.s3Service.uploadFile(
        file,
        fileName,
        bucketName,
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      );

      const createDto: CreateProductFileDto = {
        name: fileName,
        s3Key,
        s3Url,
        status: ProductFileStatus.UPLOADED,
      };

      const productFile = await this.fileRepository.create(createDto);

      // 3. Send message to SQS for processing
      await this.sendProductFileMessage({
        fileId: productFile.id,
        s3Key,
        fileName,
        action: "PROCESS_EXCEL_PRODUCT_FILE",
      });

      this.logger.log(
        `File upload completed: ${fileName} (ID: ${productFile.id})`,
      );
      return productFile;
    } catch (error) {
      this.logger.error(`Failed to upload file: ${error}`);
      throw error;
    }
  }

  async findAll(): Promise<ProductFileResponseDto[]> {
    return this.fileRepository.findAll();
  }

  async findById(id: number): Promise<ProductFileResponseDto | null> {
    return this.fileRepository.findById(id);
  }

  async updateStatus(
    id: number,
    status: ProductFileStatus,
  ): Promise<ProductFileResponseDto> {
    return this.fileRepository.updateStatus(id, status);
  }

  private async sendProductFileMessage(
    message: IProductFileMessage,
  ): Promise<void> {
    const queueUrl = this.config.fileUploadQueueUrl;

    if (queueUrl === undefined || queueUrl === "") {
      this.logger.error("File processing queue URL not configured");
      throw new Error("File processing queue URL not configured");
    }

    try {
      await this.sqsService.send("product-file-upload", {
        id: message.fileId.toString(),
        body: JSON.stringify(message),
        messageAttributes: {
          action: { DataType: "String", StringValue: message.action },
          fileId: {
            DataType: "String",
            StringValue: message.fileId.toString(),
          },
        },
      });
      this.logger.log(
        `File processing message sent for file ID: ${message.fileId}`,
      );
    } catch (error) {
      this.logger.error(`Failed to send file processing message: ${error}`);
      throw error;
    }
  }
}
