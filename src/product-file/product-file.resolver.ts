import { Logger } from "@nestjs/common";
import { Args, Mutation, Query, Resolver } from "@nestjs/graphql";
import { Public } from "@sw-ecom360/common-module";
import { FileValidationPipe } from "./pipes/file-validation.pipe";
import { ProductFileService } from "./services/product-file.service";

@Resolver("ProductFile")
export class ProductFileResolver {
  private readonly logger = new Logger(ProductFileResolver.name);

  constructor(private readonly ProductFileService: ProductFileService) {}

  @Query("productFiles")
  async findAll() {
    return this.ProductFileService.findAll();
  }

  @Query("productFile")
  async findOne(@Args("id") id: number) {
    return this.ProductFileService.findById(id);
  }

  @Mutation("uploadProductsExcelFile")
  @Public()
  async uploadProductsExcelFile(@Args("file", FileValidationPipe) file: File) {
    try {
      const arrayBuffer = await file.arrayBuffer();
      const buffer = Buffer.from(arrayBuffer);

      this.logger.log(`Processing products Excel file upload: ${file.name}`);
      const result = await this.ProductFileService.uploadExcelFile(
        buffer,
        file.name,
      );

      return {
        success: true,
        message:
          "Products Excel file uploaded successfully and queued for processing",
        file: result,
      };
    } catch (error) {
      this.logger.error(`Products Excel file upload failed: ${error}`);

      return {
        success: false,
        message: "Products Excel file upload failed",
        file: null,
      };
    }
  }
}
