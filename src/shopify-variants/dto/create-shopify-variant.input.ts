import { IsBigInt } from "@sw-ecom360/common-module";
import { Transform, Type } from "class-transformer";
import {
  ArrayMaxSize,
  IsArray,
  IsBoolean,
  IsDateString,
  IsInt,
  IsNumber,
  IsOptional,
  IsString,
  <PERSON><PERSON>ength,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>idateNested,
} from "class-validator";
import { ShopifyMediaInput } from "../../shopify-products/dto/shopify-media.input";

export class CreateShopifyVariantInput {
  @IsBigInt()
  shopifyId: bigint;

  @IsString()
  @MinLength(1)
  @MaxLength(255)
  @Transform(({ value }) =>
    typeof value === "string" ? value.trim() : undefined,
  )
  title: string;

  @IsString()
  @MinLength(1)
  @MaxLength(255)
  @Transform(({ value }) =>
    typeof value === "string" ? value.trim() : undefined,
  )
  displayName: string;

  @IsOptional()
  @IsString()
  @MaxLength(16)
  @Transform(({ value }) =>
    typeof value === "string" ? value.trim() : undefined,
  )
  sku?: string;

  @IsOptional()
  @IsString()
  @MaxLength(50)
  barcode?: string;

  @IsNumber({}, { message: "Price must be a valid number" })
  @Min(0, { message: "Price cannot be negative" })
  @Type(() => Number)
  price: number;

  @IsOptional()
  @IsNumber({}, { message: "Compare at price must be a valid number" })
  @Min(0, { message: "Compare at price cannot be negative" })
  @Type(() => Number)
  compareAtPrice?: number;

  @IsInt()
  @Min(1)
  @Type(() => Number)
  position: number;

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (value === "true") return true;
    if (value === "false") return false;
    return Boolean(value);
  })
  taxable?: boolean;

  @IsOptional()
  @IsArray()
  @ArrayMaxSize(10)
  @ValidateNested({ each: true })
  @Type(() => ShopifyMediaInput)
  media?: ShopifyMediaInput[];

  @IsOptional()
  @IsInt()
  @Type(() => Number)
  inventoryQuantity?: number;

  @IsOptional()
  @IsBigInt()
  inventoryItemId?: bigint;

  @IsOptional()
  @IsDateString()
  shopifyCreatedAt?: string;

  @IsOptional()
  @IsDateString()
  shopifyUpdatedAt?: string;

  @IsBigInt()
  shopifyProductId: bigint;

  @IsOptional()
  @IsInt()
  @Type(() => Number)
  variantId?: number;
}
