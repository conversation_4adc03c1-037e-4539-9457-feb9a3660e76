import { DynamicModule, Module, Type } from "@nestjs/common";
import { ConfigModule } from "./config";
import commonAwsConfig from "./config/configuration/common-aws.config";
import { S3CommandFactoriesProvider } from "./factories/s3-command.factory";
import { IDatabaseService } from "./interfaces";
import { PaginationService, QueryMapperService, S3Service } from "./services";

@Module({})
export class CommonModule {
  static forRoot(databaseService: Type<IDatabaseService>): DynamicModule {
    return {
      global: true,
      module: CommonModule,
      imports: [ConfigModule],
      providers: [
        QueryMapperService,
        PaginationService,
        S3CommandFactoriesProvider,
        {
          provide: "DB_SERVICE",
          useClass: databaseService,
        },
        {
          provide: "commonAws",
          useFactory: () => commonAwsConfig(),
        },
        S3Service,
      ],
      exports: [QueryMapperService, PaginationService, S3Service],
    };
  }
}
