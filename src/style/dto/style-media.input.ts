import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>al,
  <PERSON><PERSON><PERSON>,
  <PERSON>Url,
  <PERSON><PERSON><PERSON><PERSON>,
} from "class-validator";
import { MediaType } from "../types/media-types";
import type { ProductMedia } from "types/prisma-types";

export class StyleMediaInput implements ProductMedia {
  @IsUrl({}, { message: "Must be a valid URL" })
  url: string;

  @IsOptional()
  @IsString()
  @MaxLength(255)
  alt?: string;

  @IsEnum(MediaType, { message: "Type must be image or video" })
  type: MediaType;
}
