import { IsInt, IsString } from "class-validator";

export class DownloadResponseDto {
  @IsString()
  downloadUrl: string;

  @IsString()
  fileName: string;

  @IsInt()
  expiresIn: number;
}

export class ExcelRowDataDto {
  @IsString()
  styleName: string;

  @IsString()
  productName: string;

  @IsString()
  sku: string;

  @IsString()
  dateCreated: string;

  @IsString()
  dateUpdated: string;
}
