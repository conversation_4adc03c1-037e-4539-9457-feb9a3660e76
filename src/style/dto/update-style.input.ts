import { PartialType } from "@nestjs/mapped-types";
import { Transform } from "class-transformer";
import {
  IsInt,
  IsOptional,
  IsString,
  MaxLength,
  MinLength,
} from "class-validator";
import { CreateStyleInput } from "./create-style.input";

export class UpdateStyleInput extends PartialType(CreateStyleInput) {
  @IsInt()
  id: number;

  @IsOptional()
  @IsString()
  @MinLength(1, { message: "Product name is required" })
  @MaxLength(255, { message: "Product name cannot exceed 255 characters" })
  @Transform(({ value }) =>
    typeof value === "string" ? value.trim() : undefined,
  )
  styleName?: string;
}
