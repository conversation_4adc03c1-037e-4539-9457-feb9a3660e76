import { Transform, Type } from "class-transformer";
import { ProductMedia } from "src/models/product-media.entity";
import { ProductResponseDto } from "src/product/dto/product-response.dto";

interface IProductSource {
  products?: ProductResponseDto[];
  shopifyProducts?: unknown[];
}

function isProductSource(obj: unknown): obj is IProductSource {
  return obj !== null && typeof obj === "object";
}

export class StyleResponseDto {
  id: number;
  styleName: string;

  @Type(() => ProductMedia)
  media: ProductMedia[];

  publishedToShopify: boolean;

  createdAt: Date;
  updatedAt: Date;

  // Computed fields
  @Transform(({ obj }) =>
    isProductSource(obj) ? (obj.products?.length ?? 0) : 0,
  )
  productCount: number;

  @Transform(({ obj }) =>
    isProductSource(obj) ? (obj.shopifyProducts?.length ?? 0) : 0,
  )
  shopifyProductCount: number;

  // Relations (populated by field resolvers)
  products?: ProductResponseDto[];
  shopifyProducts?: unknown[];
}
