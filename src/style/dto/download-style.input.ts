import { Type } from "class-transformer";
import { <PERSON>A<PERSON>y, IsInt, IsOptional, IsString } from "class-validator";
import { StyleWhereDto } from "./style-where.dto";

export class DownloadSelectedStylesInput {
  @IsArray()
  @IsInt({ each: true })
  @Type(() => Number)
  styleIds: number[];
}

export class DownloadAllStylesInput {
  @IsOptional()
  @Type(() => StyleWhereDto)
  where?: StyleWhereDto;

  @IsOptional()
  @IsString()
  quickSearchQuery?: string;
}
