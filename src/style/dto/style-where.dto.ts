import { IWhereEntityInput, WhereInput } from "@sw-ecom360/common-module";
import { Type } from "class-transformer";
import { IsOptional, ValidateNested } from "class-validator";

export class StyleWhereDto implements IWhereEntityInput {
  [key: string]: WhereInput | undefined;

  @IsOptional()
  @ValidateNested()
  @Type(() => WhereInput)
  id?: WhereInput;

  @IsOptional()
  @ValidateNested()
  @Type(() => WhereInput)
  styleName?: WhereInput;
}
