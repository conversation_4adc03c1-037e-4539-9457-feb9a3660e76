import { Transform, Type } from "class-transformer";
import {
  ArrayMaxSize,
  IsArray,
  IsOptional,
  IsString,
  <PERSON><PERSON>ength,
  <PERSON><PERSON><PERSON><PERSON>,
  ValidateNested,
} from "class-validator";
import { StyleMediaInput } from "./style-media.input";

export class CreateStyleInput {
  @IsString()
  @MinLength(1, { message: "Product name is required" })
  @MaxLength(255, { message: "Product name cannot exceed 255 characters" })
  @Transform(({ value }) =>
    typeof value === "string" ? value.trim() : undefined,
  )
  styleName: string;

  @IsOptional()
  @IsArray()
  @ArrayMaxSize(20, { message: "Cannot have more than 20 media items" })
  @ValidateNested({ each: true })
  @Type(() => StyleMediaInput)
  media?: StyleMediaInput[];
}
