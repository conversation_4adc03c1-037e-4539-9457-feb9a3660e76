import { Injectable, Logger, Inject } from "@nestjs/common";
import { ConfigType } from "@nestjs/config";
import { S3Service } from "@sw-ecom360/common-module";
import * as ExcelJS from "exceljs";
import awsConfig from "src/config/configuration/aws.config";
import {
  DownloadResponseDto,
  ExcelRowDataDto,
} from "../dto/download-style.dto";
import {
  DownloadAllStylesInput,
  DownloadSelectedStylesInput,
} from "../dto/download-style.input";
import { StyleResponseDto } from "../dto/style-response.dto";
import { StyleRepository } from "../repositories/style.repository";

@Injectable()
export class DownloadService {
  private readonly logger = new Logger(DownloadService.name);

  constructor(
    private readonly styleRepository: StyleRepository,
    private readonly s3Service: S3Service,
    @Inject(awsConfig.KEY)
    private readonly config: ConfigType<typeof awsConfig>,
  ) {}

  async downloadSelectedStyles(
    input: DownloadSelectedStylesInput,
  ): Promise<DownloadResponseDto> {
    this.logger.log(`Downloading ${input.styleIds.length} selected styles`);

    const styles = await this.styleRepository.findMany(input.styleIds, {
      products: true,
    });

    return this.generateExcelAndUpload(styles, "selected-styles");
  }

  async downloadAllStyles(
    input: DownloadAllStylesInput,
  ): Promise<DownloadResponseDto> {
    this.logger.log("Downloading all styles with filters");

    const styles = await this.styleRepository.findAll(
      input.where,
      input.quickSearchQuery,
      { products: true },
    );

    return this.generateExcelAndUpload(styles, "all-styles");
  }

  private async generateExcelAndUpload(
    styles: StyleResponseDto[],
    filePrefix: string,
  ): Promise<DownloadResponseDto> {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet("Styles Export");

    // Define headers with nice formatting
    const headers = [
      { header: "Style Name", key: "styleName", width: 30 },
      { header: "Product Name", key: "productName", width: 30 },
      { header: "SKU", key: "sku", width: 20 },
      { header: "Date Created", key: "dateCreated", width: 20 },
      { header: "Date Updated", key: "dateUpdated", width: 20 },
    ];

    worksheet.columns = headers;

    // Style the header row
    worksheet.getRow(1).font = { bold: true };
    worksheet.getRow(1).fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "FFE0E0E0" },
    };

    // Flatten the data structure
    const flattenedData: ExcelRowDataDto[] = [];

    styles.forEach((style) => {
      if (style.products && style.products.length > 0) {
        style.products.forEach((product) => {
          flattenedData.push({
            styleName: style.styleName,
            productName: product.productName,
            sku: product.sku,
            dateCreated: new Date(style.createdAt).toLocaleDateString(),
            dateUpdated: new Date(style.updatedAt).toLocaleDateString(),
          });
        });
      } else {
        // Style without products
        flattenedData.push({
          styleName: style.styleName,
          productName: "",
          sku: "",
          dateCreated: new Date(style.createdAt).toLocaleDateString(),
          dateUpdated: new Date(style.updatedAt).toLocaleDateString(),
        });
      }
    });

    // Add data to worksheet
    worksheet.addRows(flattenedData);

    // Generate Excel buffer
    const buffer = await workbook.xlsx.writeBuffer();

    // Generate filename with timestamp
    const timestamp = Date.now();
    const fileName = `${filePrefix}-export-${timestamp}.xlsx`;

    // Upload to S3
    const bucketName = this.config.downloadBucket;
    if (bucketName === undefined) {
      throw new Error("Download bucket not configured");
    }

    const { s3Key } = await this.s3Service.uploadFile(
      Buffer.from(buffer),
      fileName,
      bucketName,
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      "exports",
    );

    const expiresIn = 24 * 3600; // 1 day
    const downloadUrl = await this.s3Service.generatePresignedDownloadUrl(
      s3Key,
      bucketName,
      expiresIn,
    );

    this.logger.log(
      `Generated Excel export with ${flattenedData.length} rows: ${fileName}`,
    );

    return {
      downloadUrl,
      fileName,
      expiresIn,
    };
  }
}
