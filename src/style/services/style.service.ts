import { Message } from "@aws-sdk/client-sqs";
import { Injectable, Logger } from "@nestjs/common";
import { SqsMessageHandler } from "@ssut/nestjs-sqs";
import { PaginationInput, PaginationService } from "@sw-ecom360/common-module";
import { ProductRepository } from "src/product/repositories/product.repository";
import { CreateStyleInput } from "../dto/create-style.input";
import { PaginatedStylesResponseDto } from "../dto/paginated-styles-response.dto";
import { StyleWhereDto } from "../dto/style-where.dto";
import { UpdateStyleInput } from "../dto/update-style.input";
import { StyleRepository } from "../repositories/style.repository";
import {
  isExcelStyleMessage,
  IExcelStyleMessage,
} from "../types/message.types";

@Injectable()
export class StyleService {
  private readonly logger = new Logger(StyleService.name);

  constructor(
    private readonly styleRepository: StyleRepository,
    private readonly paginationService: PaginationService,
    private readonly productRepository: ProductRepository,
  ) {}

  async findAll(whereInput: StyleWhereDto) {
    return this.styleRepository.findAll(whereInput);
  }

  async findAllPaginated(
    pagination: PaginationInput,
    whereInput?: StyleWhereDto,
    quickSearchQuery?: string,
  ): Promise<PaginatedStylesResponseDto> {
    const paginationParams =
      this.paginationService.getPaginationParams(pagination);
    const { totalCount, styles: data } =
      await this.styleRepository.findAllPaginated(
        paginationParams,
        whereInput,
        quickSearchQuery,
      );

    const meta = this.paginationService.getPaginationMeta(
      pagination,
      totalCount,
    );

    return { meta, data };
  }

  async create(createStyleInput: CreateStyleInput) {
    return this.styleRepository.create(createStyleInput);
  }

  async update(updateStyleInput: UpdateStyleInput) {
    return this.styleRepository.update(updateStyleInput);
  }

  findOne(id: number) {
    return this.styleRepository.findOne(id);
  }

  remove(id: number) {
    return this.styleRepository.remove(id);
  }

  @SqsMessageHandler("excel-products", false)
  async processExcelProduct(message: Message) {
    this.logger.log(`Processing Excel style: ${message.MessageId}`);

    if (message.Body === undefined || message.Body === "") {
      this.logger.error("Received message without body");
      return;
    }

    const parsedData: unknown = JSON.parse(message.Body);
    if (!isExcelStyleMessage(parsedData)) {
      this.logger.error("Invalid Excel style message format");
      return;
    }

    const messageData: IExcelStyleMessage = parsedData;

    try {
      await this.persistToDb(messageData);

      this.logger.log(`Successfully processed style: ${messageData.styleName}`);
    } catch (error) {
      this.logger.error(
        `Failed to process style ${messageData.styleName}: ${error}`,
      );
      throw error;
    }
  }

  private async persistToDb(message: {
    styleName: string;
    products: { productName: string; sku: string; size?: string }[];
  }): Promise<void> {
    try {
      const style = await this.styleRepository.upsertByName(message.styleName);

      for (const product of message.products) {
        await this.productRepository.upsertByProductAndName({
          styleId: style.id,
          productName: product.productName,
          sku: product.sku,
          options:
            product.size !== undefined && product.size.trim() !== ""
              ? { size: product.size }
              : undefined,
        });
      }

      this.logger.log(`Persisted product to DB: ${message.styleName}`);
    } catch (dbErr) {
      this.logger.error(
        `DB persist failed for ${message.styleName}: ${String(dbErr)}`,
      );
      throw dbErr;
    }
  }
}
