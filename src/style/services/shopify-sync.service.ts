import { Message } from "@aws-sdk/client-sqs";
import { Inject, Injectable, Logger } from "@nestjs/common";
import { ConfigType } from "@nestjs/config";
import { SqsMessageHandler, SqsService } from "@ssut/nestjs-sqs";
import { v4 as uuidv4 } from "uuid";
import awsConfig from "src/config/configuration/aws.config";
import { ShopifyService } from "../../shopify/shopify.service";
import { StyleResponseDto } from "../dto/style-response.dto";
import { StyleRepository } from "../repositories/style.repository";
import {
  isShopifySyncMessage,
  IShopifySyncMessage,
} from "../types/shopify-sync-message.types";

@Injectable()
export class ShopifySyncService {
  private readonly logger = new Logger(ShopifySyncService.name);

  constructor(
    private readonly styleRepository: StyleRepository,
    private readonly shopifyService: ShopifyService,
    private readonly sqsService: SqsService,
    @Inject(awsConfig.KEY)
    private readonly config: ConfigType<typeof awsConfig>,
  ) {}

  /**
   * Sync all unpublished styles to Shopify
   */
  async syncUnpublishedStyles(shopKey: string): Promise<void> {
    this.logger.log("Starting sync of unpublished styles to Shopify");

    try {
      const unpublishedStyles = await this.styleRepository.findUnpublished();

      this.logger.log(`Found ${unpublishedStyles.length} unpublished styles`);

      for (const style of unpublishedStyles) {
        await this.sendShopifySyncMessage({
          styleId: style.id,
          styleName: style.styleName,
          shopKey,
          action: "SYNC_STYLE_TO_SHOPIFY",
        });
      }

      this.logger.log(
        `Queued ${unpublishedStyles.length} styles for Shopify sync`,
      );
    } catch (error) {
      this.logger.error(`Failed to sync unpublished styles: ${error}`);
    }
  }

  /**
   * Sync a specific style to Shopify
   */
  async syncStyleToShopify(styleId: number, shopKey: string): Promise<void> {
    this.logger.log(`Syncing style ${styleId} to Shopify`);

    try {
      const style = await this.styleRepository.findOne(styleId, {
        products: true,
      });
      if (!style) {
        this.logger.error(`Style with ID ${styleId} not found`);
        return;
      }

      await this.sendShopifySyncMessage({
        styleId,
        styleName: style.styleName,
        shopKey,
        action: "SYNC_STYLE_TO_SHOPIFY",
      });

      this.logger.log(`Queued style ${styleId} for Shopify sync`);
    } catch (error) {
      this.logger.error(`Failed to queue style ${styleId} for sync: ${error}`);
    }
  }

  /**
   * SQS message handler for processing Shopify sync messages
   */
  @SqsMessageHandler("shopify-products", false)
  async processShopifySyncMessage(message: Message): Promise<void> {
    this.logger.log(`Processing Shopify sync message: ${message.MessageId}`);

    if (message.Body === undefined || message.Body === "") {
      this.logger.error("Received message without body");
      return;
    }

    const parsedData: unknown = JSON.parse(message.Body);
    if (!isShopifySyncMessage(parsedData)) {
      this.logger.error("Invalid Shopify sync message format");
      return;
    }

    const messageData: IShopifySyncMessage = parsedData;

    try {
      await this.processStyleSync(messageData);
      this.logger.log(`Successfully synced style: ${messageData.styleId}`);
    } catch (error) {
      this.logger.error(
        `Failed to sync style ${messageData.styleId}: ${error}`,
      );
    }
  }

  /**
   * Process individual style sync to Shopify
   */
  private async processStyleSync(message: IShopifySyncMessage): Promise<void> {
    try {
      const style = await this.styleRepository.findOne(message.styleId, {
        products: true,
      });
      if (!style) {
        this.logger.error(`Style with ID ${message.styleId} not found`);
        return;
      }

      if (style.publishedToShopify) {
        this.logger.log(
          `Style ${style.id} already published to Shopify, skipping`,
        );
        return;
      }

      const productSet = this.prepareShopifyProductSet(style);

      const result = await this.shopifyService.bulkSetProduct(
        message.shopKey,
        productSet,
        false,
      );

      this.logger.debug(`Shopify productSet result: ${JSON.stringify(result)}`);

      await this.styleRepository.markAsPublished(style.id);

      this.logger.log(`Successfully synced style ${style.id} to Shopify`);
    } catch (error) {
      this.logger.error(`Failed to process style sync: ${error}`);
    }
  }

  /**
   * Prepare style data for Shopify API
   */
  private prepareShopifyProductSet(style: StyleResponseDto) {
    const sizes = [
      ...new Set(
        style.products
          ?.map((p) => p.options?.size)
          .filter((size): size is string => Boolean(size)) || [],
      ),
    ];

    return {
      title: style.styleName,
      productOptions: sizes.length
        ? [
            {
              name: "Size",
              position: 1,
              values: sizes.map((name) => ({ name })),
            },
          ]
        : undefined,
      variants:
        style.products?.map((product) => {
          const size = product.options?.size;
          return {
            optionValues:
              size !== undefined && size.trim() !== ""
                ? [{ optionName: "Size", name: size }]
                : [],
            sku: product.sku,
          };
        }) || [],
    };
  }

  /**
   * Send message to Shopify sync queue
   */
  private async sendShopifySyncMessage(
    message: IShopifySyncMessage,
  ): Promise<void> {
    const queueUrl = this.config.shopifyProductsQueueUrl;

    if (queueUrl === undefined) {
      this.logger.error("Shopify products queue URL not configured");
      return;
    }

    try {
      await this.sqsService.send("shopify-products", {
        id: uuidv4(),
        body: JSON.stringify(message),
        messageAttributes: {
          action: { DataType: "String", StringValue: message.action },
          styleId: {
            DataType: "String",
            StringValue: message.styleId.toString(),
          },
          shopKey: {
            DataType: "String",
            StringValue: message.shopKey,
          },
        },
      });

      this.logger.log(
        `Shopify sync message sent for style: ${message.styleId}`,
      );
    } catch (error) {
      this.logger.error(`Failed to send Shopify sync message: ${error}`);
    }
  }
}
