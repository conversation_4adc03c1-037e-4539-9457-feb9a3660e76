import { Module } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";
import { CommonModule } from "@sw-ecom360/common-module";
import awsConfig from "src/config/configuration/aws.config";
import { PrismaModule } from "src/prisma/prisma.module";
import { PrismaService } from "src/prisma/prisma.service";
import { ProductModule } from "src/product/product.module";
import { AppSqsModule } from "../queue/sqs.module";
import { ShopifyModule } from "../shopify/shopify.module";
import { StyleRepository } from "./repositories/style.repository";
import { DownloadService } from "./services/download.service";
import { ShopifySyncService } from "./services/shopify-sync.service";
import { StyleService } from "./services/style.service";
import { StyleResolver } from "./style.resolver";

@Module({
  imports: [
    CommonModule.forRoot(PrismaService),
    ConfigModule.forFeature(awsConfig),
    PrismaModule,
    AppSqsModule,
    ProductModule,
    ShopifyModule,
  ],
  providers: [
    StyleResolver,
    StyleService,
    StyleRepository,
    DownloadService,
    ShopifySyncService,
  ],
  exports: [StyleService, StyleRepository, DownloadService, ShopifySyncService],
})
export class StyleModule {}
