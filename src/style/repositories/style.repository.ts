import { Injectable } from "@nestjs/common";
import {
  PaginationParameters,
  QueryMapperService,
} from "@sw-ecom360/common-module";
import { plainToInstance } from "class-transformer";
import { PrismaService } from "src/prisma/prisma.service";
import { CreateStyleInput } from "../dto/create-style.input";
import { StyleResponseDto } from "../dto/style-response.dto";
import { StyleWhereDto } from "../dto/style-where.dto";
import { UpdateStyleInput } from "../dto/update-style.input";

@Injectable()
export class StyleRepository {
  constructor(
    private readonly prisma: PrismaService,
    private readonly queryMapperService: QueryMapperService,
  ) {}

  async findAll(
    whereInput?: StyleWhereDto,
    quickSearchQuery?: string,
    include: { products?: boolean } = {},
  ): Promise<StyleResponseDto[]> {
    const where = this.queryMapperService.mapWhereInput(
      whereInput || {},
      ["id", "shopId"],
      {
        styleName: quickSearchQuery,
      },
    );

    const styles = await this.prisma.style.findMany({
      where,
      include,
    });

    return plainToInstance(StyleResponseDto, styles);
  }

  async findAllPaginated(
    pagination: PaginationParameters,
    whereInput?: StyleWhereDto,
    quickSearchQuery?: string,
  ): Promise<{ styles: StyleResponseDto[]; totalCount: number }> {
    const where = this.queryMapperService.mapWhereInput(
      whereInput || {},
      ["id", "shopId"],
      {
        styleName: quickSearchQuery,
      },
    );

    const query = {
      where,
    };
    const [totalCount, styles] = await Promise.all([
      this.prisma.style.count(query),
      this.prisma.style.findMany({
        ...query,
        ...pagination,
        orderBy: { id: "asc" },
      }),
    ]);

    return {
      styles: plainToInstance(StyleResponseDto, styles),
      totalCount,
    };
  }

  async findOne(
    id: number,
    include: { products?: boolean } = {},
  ): Promise<StyleResponseDto | null> {
    const style = await this.prisma.style.findUnique({
      where: { id },
      include,
    });
    return style ? plainToInstance(StyleResponseDto, style) : null;
  }

  async create(createStyleInput: CreateStyleInput): Promise<StyleResponseDto> {
    const style = await this.prisma.style.create({
      data: { ...createStyleInput, collectionId: 1 },
    });
    return plainToInstance(StyleResponseDto, style);
  }

  async update(updateStyleInput: UpdateStyleInput): Promise<StyleResponseDto> {
    const { id, ...updateInput } = updateStyleInput;
    const updated = await this.prisma.style.update({
      where: { id },
      data: updateInput,
    });
    return plainToInstance(StyleResponseDto, updated);
  }

  async findByName(styleName: string): Promise<StyleResponseDto | null> {
    const style = await this.prisma.style.findFirst({
      where: { styleName },
    });
    return style ? plainToInstance(StyleResponseDto, style) : null;
  }

  async upsertByName(styleName: string): Promise<StyleResponseDto> {
    const existing = await this.findByName(styleName);
    if (existing) {
      return plainToInstance(StyleResponseDto, existing);
    }
    return await this.create({ styleName });
  }

  async remove(id: number): Promise<StyleResponseDto> {
    const removed = await this.prisma.style.delete({
      where: { id },
    });
    return plainToInstance(StyleResponseDto, removed);
  }

  delete(id: number): Promise<StyleResponseDto> {
    return this.remove(id);
  }

  async findMany(
    styleIds: number[],
    include: { products?: boolean } = {},
  ): Promise<StyleResponseDto[]> {
    const styles = await this.prisma.style.findMany({
      where: { id: { in: styleIds } },
      orderBy: { id: "asc" },
      include,
    });

    return plainToInstance(StyleResponseDto, styles);
  }

  /**
   * Find all styles that haven't been published to Shopify
   */
  async findUnpublished(): Promise<StyleResponseDto[]> {
    const styles = await this.prisma.style.findMany({
      where: { publishedToShopify: false },
      include: {
        products: true,
      },
    });
    return plainToInstance(StyleResponseDto, styles);
  }

  /**
   * Mark a style as published to Shopify
   */
  async markAsPublished(id: number): Promise<StyleResponseDto> {
    const updated = await this.prisma.style.update({
      where: { id },
      data: { publishedToShopify: true },
    });
    return plainToInstance(StyleResponseDto, updated);
  }

  /**
   * Mark a style as unpublished from Shopify
   */
  async markAsUnpublished(id: number): Promise<StyleResponseDto> {
    const updated = await this.prisma.style.update({
      where: { id },
      data: { publishedToShopify: false },
    });
    return plainToInstance(StyleResponseDto, updated);
  }
}
