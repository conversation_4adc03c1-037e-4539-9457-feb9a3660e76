import { UsePip<PERSON>, ValidationPipe } from "@nestjs/common";
import { Args, Query, Resolver, Mutation } from "@nestjs/graphql";
import { PaginationInput } from "@sw-ecom360/common-module";
import { CreateStyleInput } from "./dto/create-style.input";
import {
  DownloadSelectedStylesInput,
  DownloadAllStylesInput,
} from "./dto/download-style.input";
import { StyleWhereDto } from "./dto/style-where.dto";
import { UpdateStyleInput } from "./dto/update-style.input";
import { DownloadService } from "./services/download.service";
import { ShopifySyncService } from "./services/shopify-sync.service";
import { StyleService } from "./services/style.service";

@Resolver("Style")
@UsePipes(new ValidationPipe({ transform: true }))
export class StyleResolver {
  constructor(
    private readonly styleService: StyleService,
    private readonly downloadService: DownloadService,
    private readonly shopifySyncService: ShopifySyncService,
  ) {}

  @Query("styles")
  async findAll(
    @Args("pagination") pagination: PaginationInput,
    @Args("where") where?: StyleWhereDto,
    @Args("quickSearchQuery") quickSearchQuery?: string,
  ) {
    return this.styleService.findAllPaginated(
      pagination,
      where,
      quickSearchQuery,
    );
  }

  @Query("style")
  async findOne(@Args("id") id: number) {
    return this.styleService.findOne(id);
  }

  @Mutation("downloadSelectedStyles")
  async downloadSelectedStyles(@Args() input: DownloadSelectedStylesInput) {
    return this.downloadService.downloadSelectedStyles(input);
  }

  @Mutation("downloadAllStyles")
  async downloadAllStyles(@Args() input: DownloadAllStylesInput) {
    return this.downloadService.downloadAllStyles(input);
  }

  @Mutation("createStyle")
  async create(@Args("createStyleInput") createStyleInput: CreateStyleInput) {
    return this.styleService.create(createStyleInput);
  }

  @Mutation("updateStyle")
  async update(@Args("updateStyleInput") updateStyleInput: UpdateStyleInput) {
    return this.styleService.update(updateStyleInput);
  }

  @Mutation("syncUnpublishedStylesToShopify")
  async syncUnpublishedStylesToShopify(
    @Args("shopKey") shopKey: string = "dev1",
  ): Promise<string> {
    await this.shopifySyncService.syncUnpublishedStyles(shopKey);
    return "Sync initiated successfully";
  }

  @Mutation("syncStyleToShopify")
  async syncStyleToShopify(
    @Args("styleId") styleId: number,
    @Args("shopKey") shopKey: string = "dev1",
  ): Promise<string> {
    await this.shopifySyncService.syncStyleToShopify(styleId, shopKey);
    return "Style sync initiated successfully";
  }
}
