type StyleMedia {
  url: String!
  type: MediaType!
  alt: String
}

type Style implements Node & HasTimestamps {
  id: Int!
  styleName: String!
  media: [StyleMedia]!
  publishedToShopify: Boolean!
  createdAt: DateTime!
  updatedAt: DateTime!
}

type StyleConnection implements PaginationConnection {
  data: [Style!]!
  meta: PaginationMeta!
}

extend type Query {
  styles(
    pagination: PaginationInput!
    where: StyleWhereInput
    quickSearchQuery: String
  ): StyleConnection!

  style(id: Int!): Style
}

type DownloadResponse {
  downloadUrl: String!
  fileName: String!
  expiresIn: Int!
}

extend type Mutation {
  downloadSelectedStyles(styleIds: [Int!]!): DownloadResponse!
  downloadAllStyles(
    where: StyleWhereInput
    quickSearchQuery: String
  ): DownloadResponse!
}

input StyleWhereInput {
  id: WhereInput
  styleName: WhereInput
}

enum MediaType {
  IMAGE
  VIDEO
  EXTERNAL_VIDEO
  MODEL_3D
}

input StyleMediaInput {
  url: String!
  type: MediaType!
  alt: String
}

input CreateStyleInput {
  styleName: String!
  media: [StyleMediaInput]
}

input UpdateStyleInput {
  id: Int!
  styleName: String
  media: [StyleMediaInput]
}

type Mutation {
  createStyle(createStyleInput: CreateStyleInput): Style!
  updateStyle(updateStyleInput: UpdateStyleInput): Style!
  syncUnpublishedStylesToShopify(shopKey: String = "dev1"): String!
  syncStyleToShopify(styleId: Int!, shopKey: String = "dev1"): String!
}
