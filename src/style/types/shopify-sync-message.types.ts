export interface IShopifySyncMessage {
  styleId: number;
  styleName: string;
  shopKey: string;
  action: "SYNC_STYLE_TO_SHOPIFY";
}

export function isShopifySyncMessage(
  value: unknown,
): value is IShopifySyncMessage {
  return (
    typeof value === "object" &&
    value !== null &&
    "styleId" in value &&
    typeof value.styleId === "number" &&
    "styleName" in value &&
    typeof value.styleName === "string" &&
    "shopKey" in value &&
    typeof value.shopKey === "string" &&
    "action" in value &&
    value.action === "SYNC_STYLE_TO_SHOPIFY"
  );
}
