import {
  isNullOrUndefined,
  isObject,
  isStringOrNumber,
  isTypedArray,
} from "@sw-ecom360/common-module";
import {
  IProductMessage,
  isProductMessage,
} from "src/product/types/IProductMessage";

export interface IStyleDataMessage {
  id: string | number;
  title?: string | null;
  products: IProductMessage[];
}

// New message type for Excel-parsed products
export interface IExcelStyleMessage {
  styleName: string;
  color: string;
  products: {
    productName: string;
    sku: string;
    size: string;
  }[];
  sizes: string[];
  shopKey: string;
  action: "PROCESS_EXCEL_STYLE";
}

export function isStyleDataMessage(value: unknown): value is IStyleDataMessage {
  if (!isObject(value)) {
    return false;
  }

  return (
    "id" in value &&
    isStringOrNumber(value.id) &&
    (!("title" in value) ||
      isNullOrUndefined(value.title) ||
      typeof value.title === "string") &&
    "products" in value &&
    isTypedArray(value.products, isProductMessage)
  );
}

export function assertIsStyleDataMessage(
  value: unknown,
): asserts value is IStyleDataMessage {
  if (!isStyleDataMessage(value)) {
    throw new Error(
      "Invalid style data structure: expected object with id, optional title, and products array",
    );
  }
}

export function isExcelStyleMessage(
  value: unknown,
): value is IExcelStyleMessage {
  if (!isObject(value)) {
    return false;
  }

  return (
    "styleName" in value &&
    typeof value.styleName === "string" &&
    "color" in value &&
    typeof value.color === "string" &&
    "products" in value &&
    Array.isArray(value.products) &&
    "sizes" in value &&
    Array.isArray(value.sizes) &&
    "shopKey" in value &&
    typeof value.shopKey === "string" &&
    "action" in value &&
    value.action === "PROCESS_EXCEL_PRODUCT"
  );
}

export function assertIsExcelVariantMessage(
  value: unknown,
): asserts value is IExcelStyleMessage {
  if (!isExcelStyleMessage(value)) {
    throw new Error(
      "Invalid Excel variant message structure: expected object with styleName, color, products, sizes, shopKey, and action",
    );
  }
}
