import { Inject, Injectable } from "@nestjs/common";
import { snakeCase } from "lodash";
import { OrderByInput, IWhereEntityInput, WhereOperator } from "../dto";
import type { IDatabaseService } from "../interfaces";

export type NumberConvertibleFields = string[];

@Injectable()
export class QueryMapperService {
  constructor(
    @Inject("DB_SERVICE") private readonly databaseService: IDatabaseService,
  ) {}

  mapWhereInput(
    whereInputs: IWhereEntityInput,
    numberConvertibleFields?: NumberConvertibleFields,
    quickSearchQuery?: Record<string, string | undefined>,
  ): Record<string, unknown> {
    const where = Object.entries(whereInputs).reduce<Record<string, unknown>>(
      (acc, [field, whereInput]) => {
        if (!whereInput) {
          return acc;
        }

        const { value, operation } = whereInput;
        const parseValue = this.parseValue(
          value,
          field,
          numberConvertibleFields,
        );

        switch (operation) {
          case WhereOperator.IS:
            acc[field] = parseValue;
            break;

          // TODO see if needed
          case WhereOperator.EQUALS:
            acc[field] = parseValue;
            break;

          case WhereOperator.CONTAINS:
            acc[field] = {
              contains: parseValue,
              mode: "insensitive", // Case-insensitive search
            };
            break;

          case WhereOperator.GTE:
            acc[field] = {
              [WhereOperator.GTE.toLocaleLowerCase()]: parseValue,
            };
            break;
          case WhereOperator.LTE:
            acc[field] = {
              [WhereOperator.LTE.toLocaleLowerCase()]: parseValue,
            };
            break;
          default:
            throw new Error(
              `Unsupported operation: ${whereInput.operation} for field: ${field}`,
            );
        }

        return acc;
      },
      {},
    );

    if (quickSearchQuery !== undefined) {
      Object.entries(quickSearchQuery).forEach(([field, query]) => {
        if (query !== undefined) {
          where[field] = {
            contains: query,
            mode: "insensitive",
          };
        }
      });
    }

    return where;
  }

  mapWhereInputToSQL(
    whereInputs: IWhereEntityInput,
    numberConvertibleFields?: NumberConvertibleFields,
  ): object[] {
    const whereClauses: object[] = [];

    Object.entries(whereInputs).forEach(([field, whereInput]) => {
      if (!whereInput) {
        return;
      }

      const { value, operation } = whereInput;
      const parsedField = snakeCase(field);
      const parsedValue = this.parseValue(
        value,
        field,
        numberConvertibleFields,
      );

      switch (operation) {
        case WhereOperator.IS:
        case WhereOperator.EQUALS:
          whereClauses.push(
            this.databaseService.toSafeRawSql(
              `${parsedField} = ${parsedValue}`,
            ),
          );
          break;

        case WhereOperator.CONTAINS:
          whereClauses.push(
            this.databaseService.toSafeRawSql(
              `${parsedField} ILIKE '%${parsedValue}%'`,
            ),
          );
          break;

        case WhereOperator.GTE:
          whereClauses.push(
            this.databaseService.toSafeRawSql(
              `${parsedField} >= ${parsedValue}`,
            ),
          );
          break;

        case WhereOperator.LTE:
          whereClauses.push(
            this.databaseService.toSafeRawSql(
              `${parsedField} <= ${parsedValue}`,
            ),
          );
          break;

        default:
          throw new Error(
            `Unsupported operation: ${operation as string} for field: ${field}`,
          );
      }
    });

    return whereClauses;
  }

  mapOrderInput(orderInput?: OrderByInput): Record<string, unknown> {
    if (!orderInput) {
      return {};
    }
    const condition: Record<string, unknown> = {};
    condition[orderInput.field] = orderInput.order;

    return condition;
  }

  private parseValue(
    rawValue: string | number,
    field: string,
    numberConvertibleFields?: NumberConvertibleFields,
  ) {
    if (!numberConvertibleFields) {
      return rawValue;
    }
    if (numberConvertibleFields.includes(field)) {
      const numericValue = Number(rawValue);
      if (isNaN(numericValue)) {
        throw new Error(
          `Invalid value provided ${rawValue}: value must be a number`,
        );
      }
      return numericValue;
    }
    return rawValue;
  }
}
