import { Injectable } from "@nestjs/common";
import { PaginationInput, PaginationMeta, PaginationParameters } from "../dto";

@Injectable()
export class PaginationService {
  getPaginationParams(pagination: PaginationInput): PaginationParameters {
    const { page, pageSize } = pagination;

    const skip = page * pageSize;
    const take = pageSize;

    return { skip, take };
  }

  getPaginationMeta(
    pagination: PaginationInput,
    totalCount: number,
  ): PaginationMeta {
    const { page, pageSize } = pagination;

    const totalPages = Math.ceil(totalCount / pageSize);
    const hasNextPage = page < totalPages;
    const hasPreviousPage = page > 1;

    return {
      totalCount,
      currentPage: page,
      totalPages,
      hasNextPage,
      hasPreviousPage,
    };
  }
}
