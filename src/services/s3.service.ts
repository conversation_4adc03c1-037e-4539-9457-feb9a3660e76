import { S3Client } from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import { Inject, Injectable, Logger } from "@nestjs/common";
import { ConfigType } from "@nestjs/config";
import { v4 as uuidv4 } from "uuid";
import awsConfig from "src/config/configuration/aws.config";
import {
  IS3CommandFactories,
  S3_COMMAND_FACTORIES,
} from "../factories/s3-command.factory";

@Injectable()
export class S3Service {
  private readonly logger = new Logger(S3Service.name);
  private readonly s3Client: S3Client;

  constructor(
    private readonly config: ConfigType<typeof awsConfig>,
    @Inject(S3_COMMAND_FACTORIES)
    private readonly commandFactories: IS3CommandFactories,
  ) {
    const region = this.config.region;
    const accessKeyId = this.config.accessKeyId;
    const secretAccessKey = this.config.secretAccessKey;

    // Configure S3 client
    const clientConfig: ConstructorParameters<typeof S3Client>[0] = { region };

    if (
      accessKeyId !== undefined &&
      accessKeyId !== "" &&
      secretAccessKey !== undefined &&
      secretAccessKey !== ""
    ) {
      clientConfig.credentials = {
        accessKeyId,
        secretAccessKey,
      };
    }

    this.s3Client = new S3Client(clientConfig);
  }

  async uploadFile(
    file: Buffer,
    fileName: string,
    bucketName: string,
    contentType: string = "application/octet-stream",
    keyPrefix: string = "uploads",
  ): Promise<{ s3Key: string; s3Url: string }> {
    const s3Key = `${keyPrefix}/${Date.now()}-${uuidv4()}-${fileName}`;

    const command = this.commandFactories.putObject({
      Bucket: bucketName,
      Key: s3Key,
      Body: file,
      ContentType: contentType,
      Metadata: {
        originalName: fileName,
        uploadedAt: new Date().toISOString(),
      },
    });

    try {
      await this.s3Client.send(command);
      const s3Url = `https://${bucketName}.s3.${this.config.region}.amazonaws.com/${s3Key}`;

      this.logger.log(`File uploaded successfully: ${s3Key}`);
      return { s3Key, s3Url };
    } catch (error) {
      this.logger.error(`Failed to upload file: ${error}`);
      throw new Error(`Failed to upload file: ${error}`);
    }
  }

  async downloadFile(s3Key: string, bucketName: string): Promise<Buffer> {
    const command = this.commandFactories.getObject({
      Bucket: bucketName,
      Key: s3Key,
    });

    try {
      const response = await this.s3Client.send(command);

      if (!response.Body) {
        throw new Error("No file content received");
      }

      const chunks: Buffer[] = [];
      const stream = response.Body as unknown as NodeJS.ReadableStream;
      await new Promise<void>((resolve, reject) => {
        stream.on("data", (chunk: Buffer) => chunks.push(chunk));
        stream.on("error", reject);
        stream.on("end", () => resolve());
      });
      const buffer = Buffer.concat(chunks);

      this.logger.log(
        `File downloaded successfully: ${s3Key} (${buffer.length} bytes)`,
      );
      return buffer;
    } catch (error) {
      this.logger.error(`Failed to download file: ${error}`);
      throw new Error("Failed to download file");
    }
  }

  async deleteFile(s3Key: string, bucketName: string): Promise<void> {
    const command = this.commandFactories.deleteObject({
      Bucket: bucketName,
      Key: s3Key,
    });

    try {
      await this.s3Client.send(command);
      this.logger.log(`File deleted successfully: ${s3Key}`);
    } catch (error) {
      this.logger.error(`Failed to delete file: ${error}`);
      throw new Error("Failed to delete file");
    }
  }

  async generatePresignedDownloadUrl(
    s3Key: string,
    bucketName: string,
    expiresIn: number = 3600,
  ): Promise<string> {
    const cmd = this.commandFactories.getObject({
      Bucket: bucketName,
      Key: s3Key,
      ResponseContentDisposition: `attachment; filename="download-${Date.now()}.xlsx"`,
      ResponseContentType:
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    });

    try {
      // Type assertion to handle AWS SDK version compatibility
      const presignedUrl = await getSignedUrl(this.s3Client, cmd, {
        expiresIn,
      });
      this.logger.log(`Generated presigned URL for: ${s3Key}`);
      return presignedUrl;
    } catch (error) {
      this.logger.error(`Failed to generate presigned URL: ${error}`);
      throw new Error("Failed to generate presigned URL");
    }
  }
}
