import { Injectable } from "@nestjs/common";
import { PaginationInput, PaginationService } from "@sw-ecom360/common-module";
import { CreateProductInput } from "../dto/create-product.input";
import { PaginatedProductsResponseDto } from "../dto/paginated-products-response.dto";
import { ProductWhereDto } from "../dto/product-where.dto";
import { UpdateProductInput } from "../dto/update-product.input";
import { ProductRepository } from "../repositories/product.repository";

@Injectable()
export class ProductService {
  constructor(
    private readonly productRepository: ProductRepository,
    private readonly paginationService: PaginationService,
  ) {}

  findAll(whereInput?: ProductWhereDto) {
    return this.productRepository.findAll(whereInput);
  }

  async findAllPaginated(
    pagination: PaginationInput,
    whereInput?: ProductWhereDto,
    quickSearchQuery?: string,
  ): Promise<PaginatedProductsResponseDto> {
    const paginationParams =
      this.paginationService.getPaginationParams(pagination);

    const { totalCount, products: data } =
      await this.productRepository.findAllPaginated(
        paginationParams,
        whereInput,
        quickSearchQuery,
      );
    const meta = this.paginationService.getPaginationMeta(
      pagination,
      totalCount,
    );

    return { meta, data };
  }

  findOne(id: number) {
    return this.productRepository.findOne(id);
  }

  async create(createProductInput: CreateProductInput) {
    return this.productRepository.create(createProductInput);
  }

  update(updateProductInput: UpdateProductInput) {
    return this.productRepository.update(updateProductInput);
  }

  remove(id: number) {
    return this.productRepository.remove(id);
  }

  doesSkuExist(sku: string) {
    return this.productRepository.doesSkuExist(sku);
  }
}
