import { Modu<PERSON> } from "@nestjs/common";
import { CommonModule } from "@sw-ecom360/common-module";
import { PrismaModule } from "src/prisma/prisma.module";
import { PrismaService } from "src/prisma/prisma.service";
import { ProductResolver } from "./product.resolver";
import { ProductRepository } from "./repositories/product.repository";
import { ProductService } from "./services/product.service";

@Module({
  imports: [CommonModule.forRoot(PrismaService), PrismaModule],
  providers: [ProductResolver, ProductService, ProductRepository],
  exports: [ProductService, ProductRepository],
})
export class ProductModule {}
