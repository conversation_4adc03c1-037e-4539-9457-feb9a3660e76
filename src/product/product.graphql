type ProductMedia {
  url: String!
  type: MediaType!
  alt: String
  position: Int
}

type Product implements Node & HasTimestamps {
  id: Int!
  sku: String!
  productName: String!
  styleId: Int!
  #product: Product!
  options: JSON
  media: [ProductMedia!]!
  createdAt: DateTime!
  updatedAt: DateTime!
}

extend type Product {
  products: [Product!]
}

extend type Query {
  products(
    pagination: PaginationInput!
    where: ProductWhereInput
    quickSearchQuery: String
  ): ProductConnection!

  product(id: Int!): Product

  searchProducts(
    pagination: PaginationInput!
    query: String!
  ): ProductConnection!
}

type ProductConnection implements PaginationConnection {
  data: [Product!]!
  meta: PaginationMeta!
}

input ProductMediaInput {
  url: String!
  type: MediaType!
  alt: String
  position: Int
}

input CreateProductInput {
  sku: String!
  productName: String!
  styleId: Int!
  media: [ProductMediaInput]
}

input UpdateProductInput {
  id: Int!
  productName: String
  media: [ProductMediaInput]
}

enum WhereOperator {
  is
  gte
  lte
}

input WhereInput {
  operation: WhereOperator!
  value: String!
}

input ProductWhereInput {
  id: WhereInput
  sku: WhereInput
  productName: WhereInput
  styleId: WhereInput
}

type Mutation {
  createProduct(createProductInput: CreateProductInput!): Product!
  updateProduct(updateProductInput: UpdateProductInput!): Product!
}
