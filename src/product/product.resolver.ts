import { UsePipes, ValidationPipe } from "@nestjs/common";
import { Args, Mutation, Query, Resolver } from "@nestjs/graphql";
import { PaginationInput } from "@sw-ecom360/common-module";
import { CreateProductInput } from "./dto/create-product.input";
import { ProductWhereDto } from "./dto/product-where.dto";
import { UpdateProductInput } from "./dto/update-product.input";
import { ProductService } from "./services/product.service";

@Resolver("Product")
@UsePipes(new ValidationPipe({ transform: true }))
export class ProductResolver {
  constructor(private readonly productService: ProductService) {}

  @Query("products")
  async findAll(
    @Args("pagination") pagination: PaginationInput,
    @Args("where") where?: ProductWhereDto,
    @Args("quickSearchQuery") quickSearchQuery?: string,
  ) {
    return this.productService.findAllPaginated(
      pagination,
      where,
      quickSearchQuery,
    );
  }

  @Query("product")
  findOneProduct(@Args("id") id: number) {
    return this.productService.findOne(id);
  }

  @Mutation("createProduct")
  async create(
    @Args("createProductInput") createProductInput: CreateProductInput,
  ) {
    return this.productService.create(createProductInput);
  }

  @Mutation("updateProduct")
  async update(
    @Args("updateProductInput") updateProductInput: UpdateProductInput,
  ) {
    return this.productService.update(updateProductInput);
  }
}
