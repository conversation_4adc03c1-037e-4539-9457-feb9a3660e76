import { Injectable } from "@nestjs/common";
import {
  PaginationParameters,
  QueryMapperService,
} from "@sw-ecom360/common-module";
import { plainToInstance } from "class-transformer";
import { PrismaService } from "src/prisma/prisma.service";
import { CreateProductInput } from "../dto/create-product.input";
import { ProductResponseDto } from "../dto/product-response.dto";
import { ProductWhereDto } from "../dto/product-where.dto";
import { UpdateProductInput } from "../dto/update-product.input";

@Injectable()
export class ProductRepository {
  constructor(
    private readonly prisma: PrismaService,
    private readonly queryMapperService: QueryMapperService,
  ) {}

  async findAll(
    whereInput?: ProductWhereDto,
    productId?: bigint,
  ): Promise<ProductResponseDto[]> {
    let where: Record<string, unknown> = {};

    if (whereInput) {
      where = this.queryMapperService.mapWhereInput(whereInput, [
        "id",
        "price",
        "compareAtPrice",
        "styleId",
      ]);
    }

    if (productId !== undefined) {
      where.productId = productId;
    }

    const products = await this.prisma.product.findMany({
      where,
    });

    return plainToInstance(ProductResponseDto, products);
  }

  async findAllPaginated(
    pagination: PaginationParameters,
    whereInput?: ProductWhereDto,
    quickSearchQuery?: string,
  ): Promise<{ products: ProductResponseDto[]; totalCount: number }> {
    if (quickSearchQuery !== undefined) {
      return this.searchProducts(pagination, quickSearchQuery);
    }
    const where = whereInput
      ? this.queryMapperService.mapWhereInput(whereInput, [
          "id",
          "price",
          "compareAtPrice",
          "styleId",
        ])
      : undefined;

    const query = {
      where,
    };

    const [totalCount, products] = await Promise.all([
      this.prisma.product.count(query),
      this.prisma.product.findMany({
        ...query,
        ...pagination,
        orderBy: { id: "asc" },
      }),
    ]);

    return {
      products: plainToInstance(ProductResponseDto, products),
      totalCount,
    };
  }

  async searchProducts(
    pagination: PaginationParameters,
    query?: string,
  ): Promise<{ products: ProductResponseDto[]; totalCount: number }> {
    // Clean the query by removing special characters and normalizing whitespace
    const cleanedQuery = (query ?? "")
      .trim()
      .replace(/[^\w\s]/g, " ")
      .replace(/\s+/g, " ");

    const searchTerms = cleanedQuery
      .split(" ")
      .filter((term) => term.length > 0);

    const tsQuery = searchTerms.map((word) => `${word}:*`).join(" & ");

    const [countResult, products] = await Promise.all([
      this.prisma.$queryRaw<{ count: number }[]>`
          SELECT COUNT(*)
          FROM "products" p
          WHERE search_vector @@ to_tsquery('english', ${tsQuery})
      `,
      // Don't use `SELECT v.*`, prisma doesn't know how to interpret PSQL Vector Types and also we don't need it in the result set.
      this.prisma.$queryRaw<
        {
          id: number;
          product_name: string;
          sku: string;
          style_id: number;
          created_at: Date;
          updated_at: Date;
        }[]
      >`
          SELECT p.id,
                 p.product_name,
                 p.sku,
                 p.style_id,
                 p.created_at,
                 p.updated_at
          FROM "products" p
          WHERE search_vector @@ to_tsquery('english', ${tsQuery})
          ORDER BY p.id ASC
          LIMIT ${pagination.take} OFFSET ${pagination.skip}
      `,
    ]);

    const totalCount = Number(countResult[0]?.count ?? 0);
    return {
      products: plainToInstance(
        ProductResponseDto,
        products.map((v) => ({
          id: v.id,
          productName: v.product_name,
          sku: v.sku,
          styleId: v.style_id,
          createdAt: v.created_at,
          updatedAt: v.updated_at,
        })),
      ),
      totalCount,
    };
  }

  async findOne(id: number): Promise<ProductResponseDto> {
    const product = await this.prisma.product.findUnique({
      where: { id: id },
    });
    return plainToInstance(ProductResponseDto, product);
  }

  async remove(id: number): Promise<ProductResponseDto> {
    const removed = await this.prisma.product.delete({
      where: { id: id },
    });
    return plainToInstance(ProductResponseDto, removed);
  }

  delete(id: number): Promise<ProductResponseDto> {
    return this.remove(id);
  }

  async create(productInput: CreateProductInput): Promise<ProductResponseDto> {
    const product = await this.prisma.product.create({
      data: productInput,
    });
    return plainToInstance(ProductResponseDto, product);
  }

  async update(productInput: UpdateProductInput) {
    const { id, ...updateInput } = productInput;
    const updated = await this.prisma.product.update({
      where: { id },
      data: updateInput,
    });
    return plainToInstance(ProductResponseDto, updated);
  }

  async upsertByProductAndName({
    styleId,
    productName,
    sku,
    options,
  }: {
    styleId: number;
    productName: string;
    sku: string;
    options?: { [key: string]: string };
  }): Promise<ProductResponseDto> {
    const existing = await this.prisma.product.findFirst({
      where: { styleId, productName },
    });

    if (existing) {
      const updated = await this.prisma.product.update({
        where: { id: existing.id },
        data: { sku, options: { ...existing.options, ...options } },
      });
      return plainToInstance(ProductResponseDto, updated);
    }
    const created = await this.prisma.product.create({
      data: { styleId, productName, sku, options },
    });
    return plainToInstance(ProductResponseDto, created);
  }

  async doesSkuExist(sku: string) {
    const product = await this.prisma.product.findUnique({
      where: { sku },
    });
    return product !== null;
  }
}
