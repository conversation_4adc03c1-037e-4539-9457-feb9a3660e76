import {
  isNullOrUndefined,
  isObject,
  isStringOrNumber,
} from "@sw-ecom360/common-module";

export interface IProductMessage {
  id: string | number;
  title?: string | null;
  price: string | number;
  compare_at_price?: string | number | null;
  sku?: string | null;
}

export function isProductMessage(value: unknown): value is IProductMessage {
  if (!isObject(value)) {
    return false;
  }

  return (
    "id" in value &&
    isStringOrNumber(value.id) &&
    (!("title" in value) ||
      isNullOrUndefined(value.title) ||
      typeof value.title === "string") &&
    "price" in value &&
    isStringOrNumber(value.price) &&
    (!("compare_at_price" in value) ||
      isNullOrUndefined(value.compare_at_price) ||
      isStringOrNumber(value.compare_at_price)) &&
    (!("sku" in value) ||
      isNullOrUndefined(value.sku) ||
      typeof value.sku === "string")
  );
}
