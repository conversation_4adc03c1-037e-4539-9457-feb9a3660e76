import { Transform, Type } from "class-transformer";
import { ProductMedia } from "src/models/product-media.entity";
import { VariantOptions } from "types/prisma-types";

interface IProductSource {
  product?: unknown;
  shopifyVariants?: unknown[];
}

function isVariantSource(obj: unknown): obj is IProductSource {
  return obj !== null && typeof obj === "object";
}

export class ProductResponseDto {
  id: number;
  sku: string;
  productName: string;
  styleId: number;

  @Type(() => Object)
  options?: VariantOptions;

  @Type(() => ProductMedia)
  media: ProductMedia[];

  createdAt: Date;
  updatedAt: Date;

  // Computed fields
  @Transform(({ obj }) =>
    isVariantSource(obj) ? (obj.shopifyVariants?.length ?? 0) : 0,
  )
  shopifyVariantCount: number;

  // Relations (populated by field resolvers)
  product?: unknown;
  shopifyVariants?: unknown[];
}
