import { OmitType, PartialType } from "@nestjs/mapped-types";
import { Transform } from "class-transformer";
import {
  IsInt,
  IsOptional,
  IsString,
  MaxLength,
  MinLength,
} from "class-validator";
import { CreateProductInput } from "./create-product.input";

export class UpdateProductInput extends PartialType(
  OmitType(CreateProductInput, ["sku", "styleId"]),
) {
  @IsInt()
  id: number;

  @IsOptional()
  @IsString()
  @MinLength(1, { message: "Product name is required" })
  @MaxLength(255, { message: "Product name cannot exceed 255 characters" })
  @Transform(({ value }) =>
    typeof value === "string" ? value.trim() : undefined,
  )
  productName?: string;
}
