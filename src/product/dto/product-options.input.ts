import { Transform } from "class-transformer";
import { IsOptional, IsString, <PERSON><PERSON>ength } from "class-validator";

export class ProductOptionsInput {
  @IsOptional()
  @IsString()
  @MaxLength(100)
  @Transform(({ value }) =>
    typeof value === "string" ? value.trim() : undefined,
  )
  size?: string;

  @IsOptional()
  @IsString()
  @MaxLength(100)
  @Transform(({ value }) =>
    typeof value === "string" ? value.trim() : undefined,
  )
  flavor?: string;

  // Allow additional dynamic properties
  [key: string]: string | undefined;
}
