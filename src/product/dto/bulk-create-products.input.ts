import { Type } from "class-transformer";
import { ArrayMaxSize, IsArray, ValidateNested } from "class-validator";
import { CreateProductInput } from "./create-product.input";

export class BulkCreateProductsInput {
  @IsArray()
  @ArrayMaxSize(100, {
    message: "Cannot create more than 100 variants at once",
  })
  @ValidateNested({ each: true })
  @Type(() => CreateProductInput)
  variants: CreateProductInput[];
}
