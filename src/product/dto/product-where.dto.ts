import { IWhereEntityInput, WhereInput } from "@sw-ecom360/common-module";
import { Type } from "class-transformer";
import { IsOptional, ValidateNested } from "class-validator";

export class ProductWhereDto implements IWhereEntityInput {
  [key: string]: WhereInput | undefined;

  @IsOptional()
  @ValidateNested()
  @Type(() => WhereInput)
  id?: WhereInput;

  @IsOptional()
  @ValidateNested()
  @Type(() => WhereInput)
  sku?: WhereInput;

  @IsOptional()
  @ValidateNested()
  @Type(() => WhereInput)
  productName?: WhereInput;

  @IsOptional()
  @ValidateNested()
  @Type(() => WhereInput)
  styleId?: WhereInput;
}
