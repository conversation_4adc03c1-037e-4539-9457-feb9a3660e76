import { Global, Module } from "@nestjs/common";
import {
  ConfigFactory,
  ConfigModule as NestConfigModule,
} from "@nestjs/config";
import awsConfig from "./configuration/aws.config";
import cognitoConfig from "./configuration/cognito.config";
import { validate } from "./validation/config.validation";

const isProduction = process.env.NODE_ENV === "production";

const envFilePath = isProduction
  ? undefined
  : [
      `.env.${process.env.NODE_ENV}.local`,
      `.env.${process.env.NODE_ENV}`,
      ".env.local",
      ".env",
    ];

const load: ConfigFactory[] = [cognitoConfig, awsConfig];

@Global()
@Module({
  imports: [
    NestConfigModule.forRoot({
      isGlobal: true,
      cache: true,
      expandVariables: true,
      envFilePath,
      load,
      validate,
      ignoreEnvFile: isProduction,
    }),
  ],
})
export class ConfigModule {}
