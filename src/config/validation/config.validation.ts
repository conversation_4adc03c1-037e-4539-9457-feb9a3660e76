import { ConfigObject } from "@nestjs/config";
import { IntersectionType } from "@nestjs/mapped-types";
import { plainToInstance } from "class-transformer";
import { validateSync } from "class-validator";
import { CognitoEnviromentVariables } from "./cognito.validator";

export class EnvironmentVariables extends IntersectionType(
  CognitoEnviromentVariables,
) {}

export function validate(config: ConfigObject) {
  const instance = plainToInstance(EnvironmentVariables, config, {
    enableImplicitConversion: true,
  });

  const validationErrors = validateSync(instance, {
    skipMissingProperties: false,
    whitelist: true,
    forbidNonWhitelisted: false,
  });

  if (validationErrors.length > 0) {
    const errors = validationErrors.map((error) => {
      const constraints = Object.values(error.constraints || {}).join(", ");
      return `${error.property}: ${constraints}`;
    });

    throw new Error(`Configuration validation failed:\n${errors.join("\n")}`);
  }

  return instance;
}
