import { IsOptional, IsString } from "class-validator";

export class AwsEnvironmentVariables {
  @IsString()
  @IsOptional()
  AWS_SQS_PRODUCTS_QUEUE_URL?: string;

  @IsString()
  @IsOptional()
  AWS_SQS_PRODUCT_FILE_UPLOAD_QUEUE_URL?: string;

  @IsString()
  @IsOptional()
  AWS_SQS_SHOPIFY_PRODUCTS_QUEUE_URL?: string;

  @IsString()
  @IsOptional()
  AWS_REGION?: string;

  @IsString()
  @IsOptional()
  AWS_ACCESS_KEY_ID?: string;

  @IsString()
  @IsOptional()
  AWS_SECRET_ACCESS_KEY?: string;
}
