import {
  Inject,
  Injectable,
  Logger,
  UnauthorizedException,
} from "@nestjs/common";
import { CognitoJwtVerifier } from "aws-jwt-verify";
import { plainToInstance } from "class-transformer";
import cognitoConfig from "../config/configuration/cognito.config";
import { CognitoUserDto } from "./dto/cognito-user.dto";
import { IRequestWithUser } from "./types/request.types";
import type { ConfigType } from "@nestjs/config";

@Injectable()
export class CognitoAuthService {
  private readonly logger = new Logger(CognitoAuthService.name);
  private accessTokenVerifier: ReturnType<typeof CognitoJwtVerifier.create>;
  private idTokenVerifier: ReturnType<typeof CognitoJwtVerifier.create>;

  constructor(
    @Inject(cognitoConfig.KEY)
    private readonly config: ConfigType<typeof cognitoConfig>,
  ) {
    // Create verifiers for both token types
    this.accessTokenVerifier = CognitoJwtVerifier.create({
      userPoolId: this.config.cognitoUserPoolId ?? "",
      tokenUse: "access",
      clientId: this.config.cognitoClientId ?? "",
    });

    this.idTokenVerifier = CognitoJwtVerifier.create({
      userPoolId: this.config.cognitoUserPoolId ?? "",
      tokenUse: "id",
      clientId: this.config.cognitoClientId ?? "",
    });
  }

  /**
   * Get authenticated user from request
   */
  public async getUserFromRequest(
    request: IRequestWithUser,
  ): Promise<CognitoUserDto | null> {
    const { accessToken, idToken } = this.extractTokensFromRequest(request);

    if (accessToken.length === 0) {
      return null;
    }

    try {
      return await this.verifyTokens(accessToken, idToken);
    } catch {
      return null;
    }
  }

  private extractTokensFromRequest(request: IRequestWithUser): {
    accessToken: string;
    idToken: string;
  } {
    const { cookies } = request;

    if (
      cookies === null ||
      cookies === undefined ||
      typeof cookies !== "object"
    ) {
      return { accessToken: "", idToken: "" };
    }

    const { idToken, accessToken } = Object.entries(cookies).reduce(
      (acc, [key, value]) => {
        if (key.includes("idToken")) {
          acc.idToken = value ?? "";
        } else if (key.includes("accessToken")) {
          acc.accessToken = value ?? "";
        }
        return acc;
      },
      { idToken: "", accessToken: "" },
    );

    return { accessToken, idToken };
  }

  private async verifyTokens(
    accessToken: string,
    idToken: string,
  ): Promise<CognitoUserDto> {
    try {
      const accessPayload = await this.accessTokenVerifier.verify(accessToken);
      const idPayload = await this.idTokenVerifier.verify(idToken);
      const email = typeof idPayload.email === "string" ? idPayload.email : "";
      const emailVerified = idPayload.email_verified === true;

      // Ensure we have an email
      if (email.length === 0) {
        throw new Error("No email found in tokens - ID token may be required");
      }

      return plainToInstance(CognitoUserDto, {
        sub: accessPayload.sub,
        email: email,
        emailVerified: emailVerified === true,
        exp: accessPayload.exp,
        iat: accessPayload.iat,
      });
    } catch (error) {
      this.logger.error("Error verifying tokens:", error);
      throw new UnauthorizedException("Invalid tokens");
    }
  }
}
