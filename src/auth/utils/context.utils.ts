import { GqlExecutionContext } from "@nestjs/graphql";
import { ExecutionContext } from "@nestjs/common";
import { IRequestWithUser } from "../types/request.types";

export function isObjectWithReq(value: unknown): value is { req: unknown } {
  return (
    value !== null &&
    typeof value === "object" &&
    "req" in value &&
    value.req !== null &&
    value.req !== undefined
  );
}

export function getRequestWithContext(context: ExecutionContext) {
  const ctx = GqlExecutionContext.create(context);

  // Try to get request from GraphQL context first
  let request: IRequestWithUser | null = null;
  try {
    const gqlContext: unknown = ctx.getContext();
    if (isObjectWithReq(gqlContext)) {
      request = gqlContext.req as IRequestWithUser;
    }
  } catch {
    request = null;
  }

  // Fallback to HTTP context if GraphQL context doesn't have request
  if (!request) {
    request = context.switchToHttp().getRequest<IRequestWithUser>();
  }

  return request;
}
