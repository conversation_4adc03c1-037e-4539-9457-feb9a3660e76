import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
} from "@nestjs/common";
import { Reflector } from "@nestjs/core";
import { CognitoAuthService } from "./cognito-auth.service";
import { AUTH_CONSTANTS } from "./constants";
import { getRequestWithContext } from "./utils/context.utils";

@Injectable()
export class CognitoAuthGuard implements CanActivate {
  constructor(
    private readonly cognitoAuthService: CognitoAuthService,
    private readonly reflector: Reflector,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    // Check if route is marked as public using configurable key
    const isPublic = this.reflector.getAllAndOverride<boolean>(
      AUTH_CONSTANTS.PUBLIC_ROUTE_METADATA_KEY,
      [context.getHandler(), context.getClass()],
    );

    if (isPublic) {
      return true;
    }

    // Handle both REST and GraphQL contexts
    const request = getRequestWithContext(context);

    const user = await this.cognitoAuthService.getUserFromRequest(request);
    if (!user) {
      throw new UnauthorizedException("Authentication required");
    }

    // Attach user to request for @CurrentUser() decorator
    request.user = user;
    return true;
  }
}
