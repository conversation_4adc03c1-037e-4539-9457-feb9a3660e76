import { Module } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";
import cognitoConfig from "../config/configuration/cognito.config";
import { CognitoAuthGuard } from "./cognito-auth.guard";
import { CognitoAuthService } from "./cognito-auth.service";

@Module({
  imports: [ConfigModule.forFeature(cognitoConfig)],
  providers: [CognitoAuthService, CognitoAuthGuard],
  exports: [CognitoAuthService, CognitoAuthGuard],
})
export class CognitoAuthModule {}
