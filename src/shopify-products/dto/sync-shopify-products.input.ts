import { IsBigInt } from "@sw-ecom360/common-module";
import { Transform } from "class-transformer";
import { IsBoolean, IsOptional } from "class-validator";

export class SyncShopifyProductsInput {
  @IsOptional()
  @IsBigInt()
  @Transform(({ value }) =>
    typeof value === "string" ||
    typeof value === "number" ||
    typeof value === "bigint"
      ? BigInt(value)
      : undefined,
  )
  shopId?: bigint;

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (value === "true") return true;
    if (value === "false") return false;
    return Boolean(value);
  })
  forceSync?: boolean;

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (value === "true") return true;
    if (value === "false") return false;
    return Boolean(value);
  })
  includeVariants?: boolean;
}
