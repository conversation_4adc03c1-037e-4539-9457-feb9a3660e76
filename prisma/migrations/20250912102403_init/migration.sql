-- CreateTable
CREATE TABLE "public"."collections" (
    "id" SERIAL NOT NULL,
    "name" VARCHAR(255) NOT NULL,
    "tag" VARCHAR(255) NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "collections_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."styles" (
    "id" SERIAL NOT NULL,
    "style_name" VARCHAR(255) NOT NULL,
    "media" JSONB[],
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "collection_id" INTEGER NOT NULL,

    CONSTRAINT "styles_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."products" (
    "id" SERIAL NOT NULL,
    "sku" VARCHAR(16) NOT NULL,
    "product_name" VARCHAR(255) NOT NULL,
    "options" JSONB,
    "media" JSONB[],
    "search_vector" tsvector,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "style_id" INTEGER NOT NULL,

    CONSTRAINT "products_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."product_files" (
    "id" SERIAL NOT NULL,
    "name" VARCHAR(255) NOT NULL,
    "s3_key" VARCHAR(500) NOT NULL,
    "s3_url" VARCHAR(1000) NOT NULL,
    "status" VARCHAR(50) NOT NULL DEFAULT 'UPLOADED',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "product_files_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."shopify_products" (
    "shopify_id" BIGINT NOT NULL,
    "shop_id" BIGINT NOT NULL,
    "title" VARCHAR(255) NOT NULL,
    "handle" VARCHAR(255) NOT NULL,
    "product_type" TEXT NOT NULL,
    "vendor" TEXT NOT NULL,
    "description" TEXT,
    "status" TEXT NOT NULL,
    "tags" TEXT[],
    "is_gift_card" BOOLEAN NOT NULL DEFAULT false,
    "template_suffix" TEXT,
    "media" JSONB[],
    "seo" JSONB NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "last_synced_at" TIMESTAMP(3),
    "shopify_published_at" TIMESTAMP(3),
    "shopify_created_at" TIMESTAMP(3),
    "shopify_updated_at" TIMESTAMP(3),
    "style_id" INTEGER,

    CONSTRAINT "shopify_products_pkey" PRIMARY KEY ("shopify_id")
);

-- CreateTable
CREATE TABLE "public"."shopify_variants" (
    "shopify_id" BIGINT NOT NULL,
    "title" VARCHAR(255) NOT NULL,
    "display_name" TEXT NOT NULL,
    "sku" VARCHAR(16),
    "barcode" VARCHAR(50),
    "price" DOUBLE PRECISION NOT NULL,
    "compare_at_price" DOUBLE PRECISION,
    "position" INTEGER NOT NULL,
    "taxable" BOOLEAN NOT NULL DEFAULT true,
    "search_vector" tsvector,
    "media" JSONB[],
    "inventory_quantity" INTEGER,
    "inventory_item_id" BIGINT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "last_synced_at" TIMESTAMP(3),
    "shopify_created_at" TIMESTAMP(3),
    "shopify_updated_at" TIMESTAMP(3),
    "shopify_product_id" BIGINT NOT NULL,
    "product_id" INTEGER,

    CONSTRAINT "shopify_variants_pkey" PRIMARY KEY ("shopify_id")
);

-- CreateIndex
CREATE INDEX "collections_name_idx" ON "public"."collections"("name");

-- CreateIndex
CREATE UNIQUE INDEX "products_sku_key" ON "public"."products"("sku");

-- CreateIndex
CREATE INDEX "products_sku_idx" ON "public"."products"("sku");

-- CreateIndex
CREATE INDEX "products_search_vector_idx" ON "public"."products" USING GIN ("search_vector");

-- CreateIndex
CREATE INDEX "shopify_products_handle_idx" ON "public"."shopify_products"("handle");

-- CreateIndex
CREATE INDEX "shopify_products_style_id_idx" ON "public"."shopify_products"("style_id");

-- CreateIndex
CREATE INDEX "shopify_products_shop_id_idx" ON "public"."shopify_products"("shop_id");

-- CreateIndex
CREATE INDEX "shopify_variants_shopify_product_id_idx" ON "public"."shopify_variants"("shopify_product_id");

-- CreateIndex
CREATE INDEX "shopify_variants_sku_idx" ON "public"."shopify_variants"("sku");

-- CreateIndex
CREATE INDEX "shopify_variants_position_idx" ON "public"."shopify_variants"("position");

-- CreateIndex
CREATE INDEX "shopify_variants_product_id_idx" ON "public"."shopify_variants"("product_id");

-- CreateIndex
CREATE INDEX "shopify_variants_search_vector_idx" ON "public"."shopify_variants" USING GIN ("search_vector");

-- AddForeignKey
ALTER TABLE "public"."styles" ADD CONSTRAINT "styles_collection_id_fkey" FOREIGN KEY ("collection_id") REFERENCES "public"."collections"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."products" ADD CONSTRAINT "products_style_id_fkey" FOREIGN KEY ("style_id") REFERENCES "public"."styles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."shopify_products" ADD CONSTRAINT "shopify_products_style_id_fkey" FOREIGN KEY ("style_id") REFERENCES "public"."styles"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."shopify_variants" ADD CONSTRAINT "shopify_variants_shopify_product_id_fkey" FOREIGN KEY ("shopify_product_id") REFERENCES "public"."shopify_products"("shopify_id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."shopify_variants" ADD CONSTRAINT "shopify_variants_product_id_fkey" FOREIGN KEY ("product_id") REFERENCES "public"."products"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- Create a function to update the search vector
CREATE OR REPLACE FUNCTION products_search_vector_update() RETURNS trigger AS $$
BEGIN
    NEW.search_vector = to_tsvector('english', coalesce(NEW.product_name, '') || ' ' || coalesce(NEW.sku, ''));
    RETURN NEW;
END
$$ LANGUAGE plpgsql;

-- Create a trigger to automatically update the search vector
CREATE TRIGGER products_search_vector_update
    BEFORE INSERT OR UPDATE ON "products"
    FOR EACH ROW EXECUTE FUNCTION products_search_vector_update();