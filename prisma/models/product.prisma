model Collection {
  id   Int    @id @default(autoincrement()) @map("id")
  name String @map("name") @db.VarChar(255)
  tag  String @map("tag") @db.VarChar(255)

  // Timestamps
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  styles Style[]

  @@index([name])
  @@map("collections")
}

model Style {
  id        Int    @id @default(autoincrement()) @map("id")
  styleName String @map("style_name") @db.VarChar(255)

  /// [ProductMedia]
  media Json[]

  // Shopify sync status
  publishedToShopify Boolean @default(false) @map("published_to_shopify")

  // Timestamps
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  products        Product[]
  shopifyProducts ShopifyProduct[]
  collection      Collection       @relation(fields: [collectionId], references: [id], onDelete: Cascade)
  collectionId    Int              @map("collection_id")

  @@map("styles")
}

model Product {
  id          Int    @id @default(autoincrement()) @map("id")
  sku         String @unique @map("sku") @db.VarChar(16)
  productName String @map("product_name") @db.VarChar(255)

  /// [VariantOptions]
  options Json?

  /// [VariantMedia]
  media Json[]

  searchVector Unsupported("tsvector")? @map("search_vector")

  // Timestamps
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  style   Style @relation(fields: [styleId], references: [id], onDelete: Cascade)
  styleId Int   @map("style_id")

  shopifyVariants ShopifyVariant[]

  @@index([sku])
  @@index(fields: [searchVector], name: "products_search_vector_idx", type: Gin)
  @@map("products")
}

model ProductFile {
  id     Int    @id @default(autoincrement()) @map("id")
  name   String @map("name") @db.VarChar(255)
  s3Key  String @map("s3_key") @db.VarChar(500)
  s3Url  String @map("s3_url") @db.VarChar(1000)
  status String @default("UPLOADED") @map("status") @db.VarChar(50)

  // Timestamps
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@map("product_files")
}
